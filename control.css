* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #ffffff;
    min-height: 100vh;
    color: #000000;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 40px 20px;
    min-height: 100vh;
}

header {
    background: #ffffff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 2px solid #000000;
}

h1 {
    color: #000000;
    font-size: 28px;
    font-weight: 600;
    letter-spacing: -0.5px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
}

#recording-status {
    font-weight: 500;
    color: #000000;
    font-size: 15px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ffffff;
    border: 2px solid #000000;
    transition: all 0.3s ease;
}

.status-dot.recording {
    background: #000000;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

section {
    background: #ffffff;
    border-radius: 12px;
    padding: 40px;
    border: 2px solid #000000;
}

h2 {
    color: #000000;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 25px;
    letter-spacing: -0.4px;
}

.option-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.option-card {
    cursor: pointer;
    border: 2px solid #000000;
    border-radius: 12px;
    padding: 24px;
    transition: all 0.2s ease;
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

.option-card:hover {
    background: #000000;
    color: #ffffff;
}

.option-card:hover .card-content {
    color: #ffffff;
}

.option-card input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.option-card input[type="radio"]:checked + .card-content {
    color: #000000;
    font-weight: 600;
}

.option-card input[type="radio"]:checked + .card-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #000000;
}

.card-content {
    text-align: center;
    position: relative;
}

.card-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.card-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.card-description {
    font-size: 14px;
    color: #000000;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.checkbox-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.checkbox-option:hover {
    background-color: #000000;
    color: #ffffff;
}

.checkbox-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #000000;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    background: #ffffff;
}

.checkbox-option input[type="checkbox"]:checked + .checkmark {
    background-color: #000000;
    border-color: #000000;
}

.checkbox-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.label-text {
    font-size: 14px;
    color: #000000;
}

.tab-info {
    border-left: 4px solid #000000;
}

.tab-details p {
    margin-bottom: 8px;
    font-size: 14px;
    word-break: break-all;
}

.tab-details strong {
    color: #000000;
}

.controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-width: 150px;
    justify-content: center;
    letter-spacing: -0.2px;
}

.btn:disabled {
    background: #ffffff !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
    opacity: 0.5 !important;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: #000000;
    color: #ffffff;
    border: 2px solid #000000;
}

.btn-primary:hover:not(:disabled) {
    background: #ffffff;
    color: #000000;
    transform: translateY(-1px);
}

.btn-primary:active:not(:disabled) {
    background: #000000;
    color: #ffffff;
    transform: translateY(0);
}

.btn-secondary {
    background: #ffffff;
    color: #000000;
    border: 2px solid #000000;
}

.btn-secondary:hover:not(:disabled) {
    background: #000000;
    color: #ffffff;
    transform: translateY(-1px);
}

.btn-secondary:active:not(:disabled) {
    background: #ffffff;
    color: #000000;
    transform: translateY(0);
}

.btn-success {
    background: #000000;
    color: #ffffff;
    border: 2px solid #000000;
}

.btn-success:hover:not(:disabled) {
    background: #ffffff;
    color: #000000;
    transform: translateY(-1px);
}

.btn-success:active:not(:disabled) {
    background: #000000;
    color: #ffffff;
    transform: translateY(0);
}

.btn-outline {
    background: #ffffff;
    border: 2px solid #000000;
    color: #000000;
}

.btn-outline:hover:not(:disabled) {
    background: #000000;
    color: #ffffff;
}

.results-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

@media (max-width: 768px) {
    .results-content {
        grid-template-columns: 1fr;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .option-group {
        grid-template-columns: 1fr;
    }
}

.mouse-data h3,
.recording-data h3 {
    color: #1d1d1f;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    letter-spacing: -0.3px;
}

#mouseStats {
    background: #f5f5f7;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid #e5e5e7;
}

#mouseStats p {
    margin-bottom: 8px;
    font-size: 14px;
}

#mouseVisualization {
    margin-top: 15px;
    text-align: center;
}

#mouseVisualization canvas,
#mouseVisualization img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
